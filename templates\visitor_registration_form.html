<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visitor Registration - {{ organisation.name }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Inter', sans-serif; }
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 600; }
        .form-group input, .form-group textarea { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #d1d5db; 
            border-radius: 0.5rem; 
        }
        .btn-primary { 
            background-color: #00bfa5; 
            color: white; 
            padding: 0.75rem 1.5rem; 
            border-radius: 0.5rem; 
            border: none; 
            font-weight: 600; 
            cursor: pointer; 
        }
        .btn-primary:hover { background-color: #00a693; }
        .info-display { 
            background-color: #f0f9ff; 
            padding: 1rem; 
            border-radius: 0.5rem; 
            margin-bottom: 1rem; 
            border-left: 4px solid #00bfa5; 
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900">Visitor Registration</h2>
                <p class="mt-2 text-sm text-gray-600">Please fill in your details to register your visit</p>
            </div>
            
            <!-- Organisation info display -->
            <div class="info-display">
                <strong>Visiting:</strong> {{ organisation.name }}
                {% if organisation.company_name %}
                    <br><small>{{ organisation.company_name }}</small>
                {% endif %}
            </div>
            
            <!-- Flash messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} p-3 rounded mb-4 
                                    {% if category == 'error' %}bg-red-100 text-red-700 border border-red-300{% endif %}
                                    {% if category == 'success' %}bg-green-100 text-green-700 border border-green-300{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- Registration form -->
            <form method="POST" class="space-y-6">
                <div class="form-group">
                    <label for="full_name">Full Name *</label>
                    <input type="text" id="full_name" name="full_name" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="company">Company/Organization *</label>
                    <input type="text" id="company" name="company" required>
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone (Optional)</label>
                    <input type="tel" id="phone" name="phone">
                </div>
                
                <div class="form-group">
                    <label for="purpose_of_visit">Purpose of Visit</label>
                    <textarea id="purpose_of_visit" name="purpose_of_visit" rows="3" placeholder="Brief description of your visit purpose"></textarea>
                </div>
                
                <!-- Hidden field - auto-populated from URL -->
                <input type="hidden" name="organisation_id" value="{{ organisation.id }}">
                
                <button type="submit" class="btn-primary w-full">Register Visit</button>
            </form>
            
            <div class="text-center text-xs text-gray-500">
                <p>Your information will be used for visitor management purposes only.</p>
            </div>
        </div>
    </div>
</body>
</html>