<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Issuer Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 400px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 20px; font-weight: 700; color: #1f2937; }
        
        /* Form Styling */
        .form-group label { display: block; margin-bottom: 6px; font-size: 14px; font-weight: 600; color: #374151; }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            margin-bottom: 15px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .btn-primary {
            width: 100%;
            padding: 12px;
            background-color: #00bfa5; /* Teal/Cyan-like color for issuance */
            color: white;
            font-weight: 700;
            border-radius: 10px;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #00897b;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">Issuer Dashboard</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-4">
                    {% for category, message in messages %}
                        <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <h2 class="text-xl font-bold mb-6 text-gray-800 border-b pb-2">Issue New Credential</h2>
        
        <form method="POST" action="{{ url_for('issue_credential_action') }}">

            <div class="form-group">
                <label for="holder_email">Holder Email Address</label>
                <input type="email" id="holder_email" name="holder_email" placeholder="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="title">Credential Title</label>
                <input type="text" id="title" name="title" placeholder="e.g., IoT Device Management Training" required>
            </div>
            
            <div class="form-group">
                <label for="issue_date">Issue Date</label>
                <!-- Default to today's date using the 'now' context variable -->
                <input type="date" id="issue_date" name="issue_date" value="{{ now.strftime('%Y-%m-%d') }}" required>
            </div>

            <div class="form-group">
                <label for="duration">Duration</label>
                <input type="text" id="duration" name="duration" placeholder="e.g., 5 Days / 40 Hours" required>
            </div>
            
            <div class="form-group">
                <label for="location">Location</label>
                <input type="text" id="location" name="location" value="CSIR Learning Factory" required>
            </div>

            <div class="form-group">
                <label for="instructor">Instructor</label>
                <input type="text" id="instructor" name="instructor" value="Dr M. Nkomo" required>
            </div>

            <div class="form-group">
                <label for="credential_type">Credential Type</label>
                <select id="credential_type" name="credential_type" required>
                <option value="">Select a Credential Type</option>
                {% for type in active_credential_types %}
                    <option value="{{ type.name }}">{{ type.name }}</option>
                {% endfor %}
                </select>
            </div>
            <button type="submit" class="btn-primary">⚡ Issue Credential</button>
        </form>

        <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}. This action adds an unverified credential to the Holder's account.
        </p>
    </div>
</body>
</html>
