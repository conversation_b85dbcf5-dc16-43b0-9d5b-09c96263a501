import sqlite3
from sqlite3 import IntegrityError

DATABASE = 'credentials.db'

def get_db_connection():
    """Establishes a connection to the SQLite database."""
    conn = sqlite3.connect(DATABASE, timeout=10)
    conn.row_factory = sqlite3.Row 
    return conn

def init_db():
    """Initializes the database schema."""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            full_name TEXT NOT NULL,
            location_organisation_id INTEGER,
            company TEXT,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL, -- NEW: Field to store securely hashed password
            registered_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            role INTEGER DEFAULT 1, --  Default role is Holder (1)
            FOREIGN KEY (location_organisation_id) REFERENCES organisations(id)  
        );
    """)

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS credentials (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            holder_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            issue_date DATE NOT NULL,
            duration TEXT,
            location TEXT,
            instructor TEXT,
            credential_type TEXT NOT NULL,
            template_id INTEGER,
            is_verified BOOLEAN DEFAULT 0,
            blockchain_hash TEXT, -- To simulate blockchain verification
            FOREIGN KEY (holder_id) REFERENCES users(id),
            FOREIGN KEY (credential_type) REFERENCES credential_types(name)
            FOREIGN KEY (template_id) REFERENCES template_types (id)
        );
    """)

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS credential_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_by INTEGER, -- Admin who created it
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        );
    """)
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS template_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_name TEXT NOT NULL, 
            description TEXT,
            credential_type TEXT NOT NULL,
            validity_period TEXT NOT NULL, 
            location_organisation_id INTEGER,
            usage_count INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,        
            FOREIGN KEY(credential_type) REFERENCES credential_types(name),
            FOREIGN KEY (location_organisation_id) REFERENCES organisations(id)
        );
   """)

    cursor.execute("""
            CREATE TABLE IF NOT EXISTS organisations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            company_name TEXT NOT NULL,  -- Renamed from "Organisation" to "Company"
            description TEXT,
            qr_code_url TEXT,  -- For organization-specific QR codes
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """)
    cursor.execute("""
            CREATE TABLE IF NOT EXISTS visitors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            full_name TEXT NOT NULL,
            email TEXT NOT NULL,
            company TEXT NOT NULL,  -- Company they're coming from
            organisation_id INTEGER NOT NULL,  -- Auto-populated from QR/URL
            visitor_type TEXT DEFAULT 'General',  -- Learner, General, VIP
            activity_type TEXT DEFAULT 'LF Visit',  -- LF Visit, Robotics Workshop, etc.
            visit_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            phone TEXT,
            purpose_of_visit TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (organisation_id) REFERENCES organisations(id)
        );
    """)
    
    conn.commit()
    conn.close()

def get_user_credentials(user_id):
    conn = get_db_connection()
    credentials = conn.execute('SELECT * FROM credentials WHERE holder_id = ? ORDER BY issue_date DESC', (user_id,)).fetchall()
    conn.close()
    if credentials:
        keys = ['id', 'holder_id', 'title', 'issue_date', 'duration', 'location', 'instructor', 'credential_type', 'is_verified']
        return [dict(zip(keys, cred)) for cred in credentials]
    return []

if __name__ == '__main__':
    init_db()
    print("Database initialized successfully at 'credentials.db'")
