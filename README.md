CSIR Learning Factory Digital Wallet (Hybrid Wallet)

This repository contains the source code for the Digital Wallet Dashboard application, designed for managing verifiable digital credentials within a learning factory environment. The application is built using Python/Flask for the backend logic and routing, and standard HTML/CSS for a responsive frontend.

🚀 Key Features

1. User Authentication: Handles user sign-in and sign-out.

2. Credential Dashboard: Displays total and verified credential counts.

3. Credential Management: Lists recent credentials with status (Verified/Pending).

4. QR Scanning: Functionality to scan QR codes to receive new credentials.

5. Feedback Mechanism: Provides a path for users to leave feedback on their visit/experience.

📁 Project Structure (Modular Layout)
The application follows a standard Flask structure to separate concerns:

Directory/File
Purpose

app.py -
Main Flask application file. Handles routing, session management, and business logic.

database.py -
Contains database connection and helper functions (e.g., SQLite connection).

credentials.db -
The SQLite Database file storing user data and credential records (ignored in Git).

static/ -
Holds static assets like custom CSS files.

templates/ -
Contains all HTML files rendered by Flask routes.

templates/wallet_dashboard.html -
The main user dashboard displaying credential stats and recent activity.

templates/onboarding.html -
Used for new user introductions and registration flow.

templates/scan_qr.html -
The interface for receiving new credentials via QR code.

templates/feedback.html -
Form for capturing user feedback.

templates/my_credentials.html -
List view of all credentials.

templates/credential_details.html -
Detailed view of a single credential.

.gitignore -
Defines files and directories to be ignored by Git (e.g., .db, venv).

⚙️ Setup and Installation
Prerequisites
Python 3.8+

pip (Python package installer)

Steps
Clone the repository:

<NAME_EMAIL>:web3/hybrid-credential-wallet.git
cd hybrid-credential-wallet

Create and activate a virtual environment (Recommended):

python3 -m venv venv
source venv/bin/activate  # On macOS/Linux
venv\Scripts\activate     # On Windows

Install dependencies:
pip install -r requirements.txt

Run the application:

python app.py

Access the application:
The application will typically run at http://127.0.0.1:5000/.