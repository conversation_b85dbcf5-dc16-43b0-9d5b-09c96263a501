<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Visitor Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base styles inherited from your dashboard structure */
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 950px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: 700; color: #1f2937; }
        .nav-tab {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s;
            border: 1px solid #e5e7eb;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            color: #374151;
        }
        /* Active state for Visitor Management */
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }
        .role-holder { background-color: #bfdbfe; color: #1e40af; } /* Used for visitor count/status */

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .container { padding: 10px; }
            .title { font-size: 20px; }
            .controls-bar { flex-direction: column; align-items: stretch; gap: 12px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">System Administration</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-tab">Dashboard</a>
                <a href="{{ url_for('admin_organisation_management') }}" class="nav-tab">Organisation Management</a>
                <a href="{{ url_for('admin_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('admin_visitor_management') }}" class="nav-tab active">Visitor Management</a>
                <a href="{{ url_for('admin_credential_types') }}" class="nav-tab">Credentials Types</a>
                <a href="{{ url_for('admin_credential_management') }}" class="nav-tab">Credential Template Management</a>
            </div>
        </nav>
        
        <!-- Flash Messages (Assuming context carries over) -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-4">
                    {% for category, message in messages %}
                        <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">HCMS Visitor Management Dashboard</h2>

        <!-- Visitor Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <!-- Total Visitors Card -->
            <div class="bg-white p-5 rounded-lg border-l-4 border-[#00bfa5] shadow-md">
                <p class="text-sm font-medium text-gray-500">Total Registered Visitors</p>
                <p class="text-3xl font-bold text-gray-900 mt-1">{{ total_visitors }}</p>
            </div>
            <!-- Recent Visitors Card -->
            <div class="bg-white p-5 rounded-lg border-l-4 border-yellow-500 shadow-md">
                <p class="text-sm font-medium text-gray-500">New Visitors (Last 7 Days)</p>
                <p class="text-3xl font-bold text-gray-900 mt-1">{{ recent_visitors_count }}</p>
            </div>
        </div>
        
        <!-- Search and Filter Controls (Functionality Focus) -->
        <div class="bg-gray-50 p-4 rounded-lg shadow-inner mb-6 flex flex-wrap controls-bar items-center justify-between">
            <!-- Search Input -->
            <div class="relative w-full sm:w-1/3">
                <input type="text" id="visitor-search" placeholder="Search by name, email, or organization" 
                       class="w-full pl-3 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-[#00bfa5] focus:border-[#00bfa5] text-sm" 
                       onkeyup="filterTable()">
            </div>
            
            <!-- Type Filter -->
            <div class="w-full sm:w-auto">
                <select id="type-filter" class="w-full border border-gray-300 rounded-lg py-2 px-3 text-sm focus:ring-[#00bfa5] focus:border-[#00bfa5]" onchange="filterTable()">
                    <option value="all" selected>Filter by Type (All)</option>
                    <option value="VIP">VIP</option>
                    <option value="General">General</option>
                    <option value="Learner">Learner</option>
                </select>
            </div>
            
            <!-- Activity Filter -->
            <div class="w-full sm:w-auto">
                <select id="activity-filter" class="w-full border border-gray-300 rounded-lg py-2 px-3 text-sm focus:ring-[#00bfa5] focus:border-[#00bfa5]" onchange="filterTable()">
                    <option value="all" selected>Filter by Activity (All)</option>
                    <option value="LF Visit">LF Visit</option>
                    <option value="Robotics Workshop">Robotics Workshop</option>
                    <option value="Advanced Manufacturing">Advanced Manufacturing</option>
                </select>
            </div>

            <!-- Action Button -->
            <button class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-colors w-full sm:w-auto">
                Download Report (CSV)
            </button>
        </div>

        <!-- Visitors Data Table -->
        <div class="bg-white rounded-lg shadow-lg overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visitor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reg. Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Organisation</th>
                    </tr>
                </thead>
                <tbody id="visitor-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Data populated by Flask -->
                    {% for visitor in recent_visitors %}
                    {% set type_map = {0: 'Learner', 1: 'General', 2: 'VIP'} %}
                    {% set activity_map = {0: 'LF Visit', 1: 'Robotics Workshop', 2: 'Advanced Manufacturing'} %}
                    
                    {% set type_index = visitor.credential_count % 3 %}
                    {% set activity_index = visitor.id % 3 %}
                    
                    {% set registered_dt = visitor.registered_on %}
                    {% if registered_dt and registered_dt is string %}
                        {# Convert string to datetime object using the globally injected 'datetime' #}
                        {# IMPORTANT: The format string '%Y-%m-%d %H:%M:%S' MUST match your database's string format exactly! #}
                        {% set registered_dt = datetime.strptime(registered_dt, '%Y-%m-%d %H:%M:%S') %}
                    {% endif %}

                    <tr data-visitor-type="{{ type_map.get(type_index, 'General') }}" 
                        data-activity-type="{{ activity_map.get(activity_index, 'LF Visit') }}"
                        class="hover:bg-f9fafb">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ visitor.full_name }}</div>
                            <div class="text-xs text-gray-500">{{ visitor.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ visitor.organization or 'N/A' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ registered_dt.strftime('%Y-%m-%d') if registered_dt else 'N/A' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="user-role role-holder text-xs px-2 py-1 rounded-full w-fit">
                                {{ type_map.get(type_index, 'General') }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ activity_map.get(activity_index, 'LF Visit') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <a href="#" title="View Details" class="text-[#00bfa5] hover:text-indigo-600 transition-colors">
                                View
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <!-- Pagination/Footer -->
            <div class="p-4 border-t border-gray-200 text-sm text-gray-600 flex justify-between items-center">
                <p>Showing **{{ recent_visitors|length }}** of **{{ total_visitors }}** records.</p>
                <div class="flex space-x-2">
                    <!-- Pagination buttons can be wired up here if more data is fetched -->
                    <button class="px-3 py-1 border rounded-lg text-gray-500 bg-gray-100 cursor-not-allowed" disabled>Previous</button>
                    <button class="px-3 py-1 border rounded-lg bg-gray-100 hover:bg-gray-200">Next</button>
                </div>
            </div>
        </div>

        <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}
        </p>
    </div>
    
    <!-- Functionality Script -->
    <script>
        /**
         * Client-side function to filter and search the visitor table.
         * This function reads the search input and filter dropdowns, 
         * then hides/shows table rows based on the criteria.
         */
        function filterTable() {
            const searchInput = document.getElementById('visitor-search').value.toLowerCase();
            const typeFilter = document.getElementById('type-filter').value;
            const activityFilter = document.getElementById('activity-filter').value;
            const tableBody = document.getElementById('visitor-table-body');
            const rows = tableBody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                
                if (cells.length > 0) {
                    const visitorName = cells[0].textContent.toLowerCase();
                    const organization = cells[1].textContent.toLowerCase();
                    const email = cells[0].querySelector('.text-xs')?.textContent.toLowerCase() || "";
                    
                    // Get data attributes for filtering
                    const rowType = row.getAttribute('data-visitor-type');
                    const rowActivity = row.getAttribute('data-activity-type');
                    
                    // 1. Search Check
                    const searchMatch = visitorName.includes(searchInput) || 
                                        organization.includes(searchInput) ||
                                        email.includes(searchInput);
                    
                    // 2. Type Filter Check
                    const typeMatch = typeFilter === 'all' || rowType === typeFilter;
                    
                    // 3. Activity Filter Check
                    const activityMatch = activityFilter === 'all' || rowActivity === activityFilter;
                    
                    // Show row if all checks pass
                    if (searchMatch && typeMatch && activityMatch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            }
        }

        // Initialize the table with filters on load
        window.onload = filterTable;
    </script>
</body>
</html>
