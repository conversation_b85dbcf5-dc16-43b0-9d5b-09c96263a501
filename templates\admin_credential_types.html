<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Credential Types</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 950px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: 700; color: #1f2937; }
        .user-card { background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 12px; padding: 16px; margin-bottom: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }

        .nav-tab {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s;
            border: 1px solid #e5e7eb;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            color: #374151;
        }
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }

        .form-group label { display: block; margin-bottom: 6px; font-size: 14px; font-weight: 600; color: #374151; }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            margin-bottom: 15px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .btn-primary {
            width: 100%;
            padding: 12px;
            background-color: #00bfa5;
            color: white;
            font-weight: 700;
            border-radius: 10px;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #00897b;
        }

        .credential-type-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .credential-type-item:last-child {
            border-bottom: none;
        }
        .credential-type-info {
            flex: 1;
        }
        .credential-type-name {
            font-weight: 600;
            font-size: 16px;
            color: #1f2937;
        }
        .credential-type-description {
            font-size: 14px;
            color: #6b7280;
            margin-top: 2px;
        }
        .credential-type-meta {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 4px;
        }
        .credential-type-actions {
            display: flex;
            gap: 8px;
        }
        .btn-toggle {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-toggle.active {
            background-color: #fef3c7;
            color: #92400e;
        }
        .btn-toggle.active:hover {
            background-color: #fde68a;
        }
        .btn-toggle.inactive {
            background-color: #d1fae5;
            color: #065f46;
        }
        .btn-toggle.inactive:hover {
            background-color: #a7f3d0;
        }
        .btn-delete {
            background-color: #fecaca;
            color: #991b1b;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-delete:hover {
            background-color: #fca5a5;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">Credential Types Management</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-tab">Dashboard</a>
                <a href="{{ url_for('admin_organisation_management') }}" class="nav-tab">Organisation Management</a>
                <a href="{{ url_for('admin_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('admin_visitor_management') }}" class="nav-tab">Visitor Management</a>
                <a href="{{ url_for('admin_credential_types') }}" class="nav-tab active">Credentials Types</a>
                <a href="{{ url_for('admin_credential_management') }}" class="nav-tab">Credential Template Management</a>
            </div>
        </nav>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="mb-4">
                {% for category, message in messages %}
                    <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        {% endwith %}

        <h2 class="text-xl font-bold mb-6 text-gray-800 border-b pb-2">Credential Types Management</h2>

        <!-- Add New Credential Type Form -->
        <div class="user-card mb-6">
            <h3 class="text-lg font-semibold mb-4">Add New Credential Type</h3>
            <form method="POST" action="{{ url_for('admin_create_credential_type') }}" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-group">
                    <label for="type_name">Type Name</label>
                    <input type="text" id="type_name" name="name" placeholder="e.g., Advanced Workshop" required>
                </div>
                
                <div class="form-group">
                    <label for="type_description">Description</label>
                    <input type="text" id="type_description" name="description" placeholder="Brief description">
                </div>

                <div class="form-group">
                    <label for="color_code">Color Code</label>
                    <input type="color" id="color_code" name="color_code" value="#6B7280">
                </div>

                <div class="form-group">
                    <label for="icon">Icon</label>
                    <input type="text" id="icon" name="icon" placeholder="🎓" value="🎓" maxlength="2">
                </div>
                
                <div class="col-span-full">
                    <button type="submit" class="btn-primary">Add Credential Type</button>
                </div>
            </form>
        </div>

        <!-- Existing Credential Types List -->
        <div class="user-card mb-6">
            <h3 class="text-lg font-semibold mb-4">Existing Credential Types ({{ credential_types|length if credential_types else 0 }})</h3>
            {% if credential_types %}
                {% for type in credential_types %}
                <div class="credential-type-item">
                    <div class="credential-type-info">
                        <div class="credential-type-name">
                            {{ type.icon or '🎓' }} {{ type.name }}
                        </div>
                        <div class="credential-type-description">{{ type.description or 'No description' }}</div>
                        <div class="credential-type-meta">
                            Created by: {{ type.created_by_name or 'Unknown' }} | 
                            Status: {% if type.is_active %}Active{% else %}Inactive{% endif %} |
                            ID: {{ type.id }}
                        </div>
                    </div>
                    <div class="credential-type-actions">
                        <form method="POST" action="{{ url_for('admin_toggle_credential_type') }}" class="inline">
                            <input type="hidden" name="type_id" value="{{ type.id }}">
                            <button type="submit" class="btn-toggle {% if type.is_active %}active{% else %}inactive{% endif %}">
                                {% if type.is_active %}Deactivate{% else %}Activate{% endif %}
                            </button>
                        </form>
                        <form method="POST" action="{{ url_for('admin_delete_credential_type') }}" class="inline" 
                            onsubmit="return confirm('Are you sure you want to delete this credential type?')">
                            <input type="hidden" name="type_id" value="{{ type.id }}">
                            <button type="submit" class="btn-delete">
                                Delete
                            </button>
                        </form>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <p class="text-gray-500 text-center py-4">No credential types defined yet.</p>
            {% endif %}
        </div>

        <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}
        </p>
    </div>
</body>
</html>