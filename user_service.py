from werkzeug.security import generate_password_hash, check_password_hash
from sqlite3 import IntegrityError, Row
from database import get_db_connection
from datetime import datetime


def get_user_by_id(user_id):
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    conn.close()
    if user:
        keys = ['id', 'full_name', 'organization', 'email', 'password_hash', 'role']
        return dict(zip(keys, user))
    return None

def get_user_by_email(email):
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE email = ?', (email,)).fetchone()
    conn.close()
    if user:
        keys = ['id', 'full_name', 'organization', 'email', 'password_hash', 'role']
        return dict(zip(keys, user))
    return None

def get_all_real_users():
    """Fetches all real users (ID < 997) for Admin role management."""

    conn = get_db_connection()
    
    users = conn.execute("""
        SELECT id, full_name, email, organization, role, email_verified 
        FROM users WHERE id < 997 
        ORDER BY created_at DESC
    """).fetchall()
    conn.close()
    
    role_map = {1: 'holder', 2: 'issuer', 3: 'verifier', 4: 'admin'}
    
    if users:
        return [
            {
                'id': u['id'], 
                'full_name': u['full_name'], 
                'email': u['email'], 
                'organization': u['organization'], 
                'role': role_map.get(u['role'], 'unknown'),
                'email_verified': bool(u['email_verified'])
            } 
            for u in users
        ]
    return []

def update_user_role(user_id, new_role):
    """Updates the role of a user based on Admin action."""
    role_map = {'holder': 1, 'issuer': 2, 'verifier': 3, 'admin': 4}
    role_id = role_map.get(new_role.lower(), 1)
    
    conn = get_db_connection()
    try:
        conn.execute('UPDATE users SET role = ? WHERE id = ?', (role_id, user_id))
        conn.commit()
        return True
    except Exception:
        return False
    finally:
        conn.close()

def update_user_profile(user_id, full_name, organization):
    """Updates a user's profile settings."""
    conn = get_db_connection()
    try:
        conn.execute("""
            UPDATE users SET full_name = ?, organization = ? WHERE id = ?
        """, (full_name, organization, user_id))
        conn.commit()
        return True
    except Exception:
        return False
    finally:
        conn.close()