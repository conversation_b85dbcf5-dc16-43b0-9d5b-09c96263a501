from flask import Flask, jsonify, render_template, redirect, url_for, request, flash, session
from database import get_db_connection, get_user_credentials
from user_service import get_user_by_id, get_user_by_email
import sqlite3
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash 
import os

app = Flask(__name__)

app.secret_key = os.urandom(24).hex()

@app.context_processor
def inject_globals():
    """
    Makes common variables like the 'datetime' class and 'now' (current time) 
    available in all templates.
    """
    return {'datetime': datetime, 'now': datetime.now()}

ONBOARDING_STEPS = {
    1: {
        'title': 'What is a Verifiable Credential?',
        'icon': '📝',
        'subtitle': 'A digital certificate that proves your participation in workshops, training sessions, and learning experiences at the CSIR Learning Factory.',
        'button_text': 'Next',
        'next_route': 'onboarding_step', 
    },
    2: {
        'title': 'Why is it valuable?',
        'icon': '⭐',
        'subtitle': 'Your credentials are tamper-proof, portable, and can be verified instantly by employers or institutions anywhere in the world.',
        'button_text': 'Next',
        'next_route': 'onboarding_step',
    },
    3: {
        'title': 'How it works',
        'icon': 'QR',
        'subtitle': 'Simply scan the QR code at any Learning Factory session, receive your credential instantly, and share it with confidence.',
        'button_text': 'Continue to Registration',
        'next_route': 'register', 
    }
}

def is_logged_in():
    """Checks if a user_id is stored in the session."""
    return 'user_id' in session

def get_current_user():
    """Retrieves the user object if logged in, or None."""
    if is_logged_in():
        return get_user_by_id(session['user_id'])
    return None

@app.route('/')
def home():
    """Renders the initial Digital Wallet screen."""
    return render_template('wallet_home.html')

@app.route('/onboarding')
def start_onboarding():
    """Redirects 'Get Started' to the first step."""
    return redirect(url_for('onboarding_step', step_id=1))

@app.route('/onboarding/<int:step_id>')
def onboarding_step(step_id):
    """Handles the display of the three onboarding screens."""
    if step_id not in ONBOARDING_STEPS:
        return redirect(url_for('home'))
    
    step_data = ONBOARDING_STEPS[step_id]
    next_step_id = step_id + 1
    
    return render_template('onboarding.html', 
                          step=step_id,
                          total_steps=len(ONBOARDING_STEPS),
                          data=step_data,
                          next_step_id=next_step_id)
    
@app.route('/register', methods=['GET', 'POST'])
def register():
    """Handles the final registration screen and form submission."""
    
    if request.method == 'POST':
        full_name = request.form.get('full_name')
        organization = request.form.get('organization')
        email = request.form.get('email')
        password = request.form.get('password')
        terms = request.form.get('terms') 
        
        if not full_name or not email or not password or terms is None:
            flash('All required fields (Name, Email, Password, and Terms) must be filled.', 'error')
            return redirect(url_for('register'))

        role = 'holder'

        password_hash = generate_password_hash(password)
        
        try:
            conn = get_db_connection()            
            cursor = conn.execute("INSERT INTO users (full_name, organization, email, password_hash, role) VALUES (?, ?, ?, ?, ?)",
                                  (full_name, organization, email, password_hash, role))
            conn.commit()
            
            user_id = cursor.lastrowid
            
            # SIMULATE CREDENTIAL ISSUANCE: Advanced Robotics Workshop (STARTS AS UNVERIFIED/PENDING)
            conn.execute("""
                INSERT INTO credentials (holder_id, title, issue_date, duration, location, instructor, credential_type, is_verified)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id,
                'Advanced Robotics Workshop',
                '2024-01-15', 
                '3 Days',
                'Pretoria Campus',
                'Workshop',
                'Dr Sarah Johnson',
                0 
            ))
            conn.commit()
            conn.close()
            
            session['user_id'] = user_id
            session['user_role'] = role 
            
            flash('Registration successful! Welcome to your digital wallet.', 'success')
            return redirect(url_for('wallet_dashboard'))
        
        except sqlite3.IntegrityError:
            flash('This email is already registered. Please sign in.', 'error')
            return redirect(url_for('register'))
        except Exception as e:
            flash(f'An error occurred: {e}', 'error')
            return redirect(url_for('register'))
            
    return render_template('register.html')

@app.route('/signin', methods=['GET', 'POST'])
def signin():
    """Handles the sign-in page and form submission."""
    if request.method == 'POST':        
        email = request.form.get('email')
        password = request.form.get('password')
        
        if not email or not password:
            flash('Please enter both email and password.', 'error')
            return render_template('signin.html')
        
        user = get_user_by_email(email)
        
        if email == '<EMAIL>' and password == 'admin':
            user = {
                'id': 999,
                'full_name': 'CSIR Admin',
                'email': email,
                'role': 'admin'
                }
        elif email == '<EMAIL>' and password == 'verify':
            user = {
                'id': 998,
                'full_name': 'CSIR Verifier',
                'email': email,
                'role': 'verifier'
                }
        elif email == '<EMAIL>' and password == 'issue':
            user = {
                'id': 997,
                'full_name': 'CSIR Issuer',
                'email': email,
                'role': 'issuer'
                }
        elif user and check_password_hash(user['password_hash'], password):
            user = {
                'id': user['id'],
                'full_name': user['full_name'],
                'email': user['email'],
                'role': 'holder'
            }
        else:
            flash('Invalid email or password. Please try again.', 'error')
            return render_template('signin.html')
        
        session['user_id'] = user['id']
        session['user_role'] = user['role']
        flash(f"Signed in successfully as a {user['role']}.", 'success')
        
        if user['role'] == 'admin':
            return redirect(url_for('admin_dashboard')) 
        elif user['role'] == 'verifier':
            return redirect(url_for('verifier_dashboard'))
        elif user['role'] == 'issuer':
            return redirect(url_for('issuer_dashboard'))
        else:
            return redirect(url_for('wallet_dashboard'))
            
    return render_template('signin.html') 

@app.route('/signout')
def signout():
    """Clears the session and logs the user out."""
    session.pop('user_id', None)
    session.pop('user_role', None)
    flash('You have been signed out.', 'info')
    return redirect(url_for('home'))

@app.route('/admin/user_management')
def admin_user_management():
    """Admin user management dashboard."""
    if session.get('user_role') != 'admin':
        flash('Access denied. You must be logged in as an Admin.', 'error')
        return redirect(url_for('signin'))
    
    conn = get_db_connection()
    
    # Get all users with organisation names
    all_users = conn.execute("""
        SELECT u.id, u.full_name, u.email, u.company, u.location_organisation_id, u.role,
               o.name as organisation_name
        FROM users u 
        LEFT JOIN organisations o ON u.location_organisation_id = o.id
        WHERE u.id < 997
    """).fetchall()
    
    # Get active organisations for dropdown
    organisations = conn.execute('SELECT * FROM organisations WHERE is_active = 1 ORDER BY name').fetchall()
    
    conn.close()
    
    available_roles = ['holder', 'issuer', 'verifier', 'admin']
    
    return render_template('admin_user_management.html',
                           user_role=session.get('user_role'),
                           all_users=all_users,
                           available_roles=available_roles,
                           organisations=organisations)
    
@app.route('/admin/dashboard')
def admin_dashboard():
    """Admin dashboard to manage users and roles."""
    
    if session.get('user_role') != 'admin':
        flash('Access denied. You must be logged in as an Admin.', 'error')
        return redirect(url_for('signin'))

    conn = get_db_connection()

    all_users = conn.execute('SELECT id, full_name, email, location_organisation_id, role FROM users WHERE id < 997').fetchall()

    template_types = conn.execute('SELECT * FROM template_types').fetchall()
    
    # Fetch credential types
    credential_types = conn.execute("""
        SELECT ct.*, u.full_name as created_by_name 
        FROM credential_types ct 
        LEFT JOIN users u ON ct.created_by = u.id 
        ORDER BY ct.created_at DESC
    """).fetchall()
    
    # Fetch active credential types for the dropdown
    active_credential_types = conn.execute("""
        SELECT * FROM credential_types 
        WHERE is_active = 1 
        ORDER BY name
    """).fetchall()
    
    conn.close()

    available_roles = ['holder', 'issuer', 'verifier', 'admin']
    
    return render_template('admin_dashboard.html', 
                           user_role=session.get('user_role'),
                           all_users=all_users,
                           available_roles=available_roles,
                           credential_types=credential_types,
                           active_credential_types=active_credential_types,
                           template_types=template_types)

@app.route('/admin/update_role', methods=['POST'])
def admin_update_role():
    """Action to update a user's role."""
    if session.get('user_role') != 'admin':
        flash('Access denied.', 'error')
        return redirect(url_for('signin'))
    
    user_id = request.form.get('user_id')
    new_role = request.form.get('new_role')
    
    if not user_id or not new_role:
        flash('Invalid request: missing user ID or new role.', 'error')
        return redirect(url_for('admin_user_management'))
        
    conn = get_db_connection()
    try:
        if int(user_id) < 997:
            conn.execute('UPDATE users SET role = ? WHERE id = ?', (new_role, user_id))
            conn.commit()
            flash(f"Role for user ID {user_id} updated to '{new_role}'.", 'success')
        else:
            flash(f"Cannot modify role for system demo user ID {user_id}.", 'error')
    except Exception as e:
        flash(f"Error updating role: {e}", 'error')
    finally:
        conn.close()

    return redirect(url_for('admin_user_management'))


@app.route('/verifier/dashboard')
def verifier_dashboard():
    """
    Verifier dashboard to view all credentials that require verification.
    """
    if session.get('user_role') != 'verifier':
        flash('Access denied. You must be logged in as a Verifier.', 'error')
        return redirect(url_for('signin'))

    conn = get_db_connection()
    
    pending_credentials = conn.execute('SELECT c.*, u.full_name FROM credentials c JOIN users u ON c.holder_id = u.id WHERE c.is_verified = 0 ORDER BY c.issue_date DESC').fetchall()
    conn.close()

    return render_template('verifier_dashboard.html', 
                           user_role=session.get('user_role'),
                           pending_credentials=pending_credentials)

@app.route('/verifier/verify/<int:cred_id>', methods=['POST'])
def verify_credential_action(cred_id):
    """Action to mark a pending credential as verified."""
    if session.get('user_role') != 'verifier':
        flash('Access denied.', 'error')
        return redirect(url_for('signin'))

    conn = get_db_connection()
    conn.execute('UPDATE credentials SET is_verified = 1 WHERE id = ?', (cred_id,))
    conn.commit()
    conn.close()

    flash(f"Credential ID {cred_id} successfully verified.", 'success')
    return redirect(url_for('verifier_dashboard'))

@app.route('/issuer/dashboard')
def issuer_dashboard():
    """Issuer dashboard (placeholder)."""
    if session.get('user_role') != 'issuer':
        flash('Access denied. You must be logged in as an Issuer.', 'error')
        return redirect(url_for('signin'))
    
    # Fetch active credential types for filtering
    conn = get_db_connection()
    active_credential_types = conn.execute("""
        SELECT * FROM credential_types 
        WHERE is_active = 1 
        ORDER BY name
    """).fetchall()
    conn.close()

    return render_template('issuer_dashboard.html', 
                           user_role=session.get('user_role'),
                           active_credential_types=active_credential_types)
                            
@app.route('/issue_credential_action', methods=['POST'])
def issue_credential_action():
    """Handles the form submission from the Issuer Dashboard to create a new credential."""
    if session.get('user_role') != 'issuer':
        flash('Access denied. Only Issuers can create credentials.', 'error')
        return redirect(url_for('signin'))

    holder_email = request.form.get('holder_email')
    title = request.form.get('title')
    issue_date = request.form.get('issue_date')
    duration = request.form.get('duration')
    location = request.form.get('location')
    instructor = request.form.get('instructor')
    credential_type = request.form.get('credential_type')

    holder = get_user_by_email(holder_email)

    if not holder:
        flash(f'Error: Holder with email "{holder_email}" not found. Credential not issued.', 'error')
        return redirect(url_for('issuer_dashboard'))
    
    conn = get_db_connection()
    try:
        conn.execute("""
            INSERT INTO credentials (holder_id, title, issue_date, duration, location, instructor, credential_type, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            holder['id'],
            title,
            issue_date,
            duration,
            location,
            instructor,
            credential_type,
            0 
        ))
        conn.commit()
        flash(f"Credential '{title}' successfully issued and sent to holder ({holder_email}). It is now pending verification.", 'success')

    except Exception as e:
        flash(f'An error occurred during credential issuance: {e}', 'error')
    finally:
        conn.close()

    return redirect(url_for('issuer_dashboard'))

@app.route('/wallet')
def wallet_dashboard():
    """Renders the main wallet dashboard, requires login."""
    
    user_role = session.get('user_role')
    
    if user_role and user_role != 'holder':
        flash('Access denied. Redirecting to your dashboard.', 'error')
        if user_role == 'admin':
            return redirect(url_for('admin_dashboard'))
        elif user_role == 'verifier':
            return redirect(url_for('verifier_dashboard'))
        elif user_role == 'issuer':
            return redirect(url_for('issuer_dashboard'))

    user = get_current_user()
    
    if not user:
        flash('Please sign in to view your wallet.', 'info')
        return redirect(url_for('signin'))

    user_id = user['id']
    
    credentials = get_user_credentials(user_id)
    
    total_credentials = len(credentials)
    verified_count = sum(1 for cred in credentials if cred['is_verified'] == 1)
    
    return render_template('wallet_dashboard.html',
                           user=user,
                           credentials=credentials,
                           total_credentials=total_credentials,
                           verified_count=verified_count)
    
@app.route('/wallet/credentials')
def my_credentials():
    """Renders the 'My Credentials' list view."""
    
    user_role = session.get('user_role')
    
    if user_role and user_role != 'holder':
        flash('Access denied. Redirecting to your dashboard.', 'error')
        if user_role == 'admin':
            return redirect(url_for('admin_dashboard'))
        elif user_role == 'verifier':
            return redirect(url_for('verifier_dashboard'))
        elif user_role == 'issuer':
            return redirect(url_for('issuer_dashboard'))

    user = get_current_user()
    if not user:
        return redirect(url_for('signin'))

    credentials = get_user_credentials(user['id'])
 
    # Fetch active credential types for filtering
    conn = get_db_connection()
    active_credential_types = conn.execute("""
        SELECT * FROM credential_types 
        WHERE is_active = 1 
        ORDER BY name
    """).fetchall()
    conn.close()
    
    return render_template('my_credentials.html', user=user, credentials=credentials,
                            active_credential_types=active_credential_types)

@app.route('/wallet/credential/<int:cred_id>')
def credential_details(cred_id):
    """Renders the single credential detail view (Slide 19, Screen 3)."""
    user = get_current_user()

    if not user:
        return redirect(url_for('signin'))

    conn = get_db_connection()
    
    credential = conn.execute('SELECT * FROM credentials WHERE id = ? AND holder_id = ?', 
                              (cred_id, user['id'])).fetchone()
    conn.close()
    
    if not credential:
        flash('Credential not found or access denied.', 'error')
        return redirect(url_for('wallet_dashboard'))
        
    return render_template('credential_details.html', 
                           credential=credential, 
                           user_id=user['id'])

@app.route('/wallet/scan')
def scan_qr():
    """Renders the QR scan screen."""
    user = get_current_user()
    
    if not user:
        return redirect(url_for('signin'))
    return render_template('scan_qr.html', user_id=user['id'])


@app.route('/wallet/receive', methods=['POST'])
def receive_credential():
    """
    Simulates receiving a new credential after scanning a QR code,
    issuing the '3D Printing' credential. This is now set to UNVERIFIED (0).
    """
    user = get_current_user()
    if not user:
        flash('User not authenticated.', 'error')
        return redirect(url_for('signin'))

    user_id = user['id']
    
    new_cred_title = "3D Printing and Additive Manufacturing"
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO credentials (holder_id, title, issue_date, duration, location, instructor, credential_type, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            user_id,
            new_cred_title,
            datetime.now().strftime('%Y-%m-%d'), 
            '2 Days',
            'Pretoria Campus',
            'Dr Elias Mokoena',
            'Technical Training',
            0 
        ))
        conn.commit()
        
        new_cred_id = cursor.lastrowid
    except Exception as e:
        conn.close()
        flash(f'Credential issuance failed: {e}', 'error')
        return redirect(url_for('wallet_dashboard'))
    finally:
        conn.close()
    
    flash(f"Credential '{new_cred_title}' successfully received! It is now pending verification.", 'success')
    return redirect(url_for('credential_received_success', cred_id=new_cred_id))


@app.route('/wallet/received/<int:cred_id>')
def credential_received_success(cred_id):
    """Renders the Credential Received notification screen (Slide 19, Screen 5)."""
    user = get_current_user()
    if not user:
        return redirect(url_for('signin'))

    conn = get_db_connection()
    credential = conn.execute('SELECT * FROM credentials WHERE id = ? AND holder_id = ?', 
                             (cred_id, user['id'])).fetchone()
    conn.close()
    
    if not credential:
        flash('Credential not found.', 'error')
        return redirect(url_for('wallet_dashboard'))

    return render_template('credential_received.html', 
                           user_id=user['id'], 
                           credential=credential)

@app.route('/wallet/feedback')
def feedback():
    """Renders the Feedback form."""
    user = get_current_user()
    if not user:
        return redirect(url_for('signin'))
    return render_template('feedback.html', user_id=user['id'])


@app.route('/wallet/submit_feedback', methods=['POST'])
def submit_feedback():
    """
    Handles feedback submission, and then initiates the credential proof request.
    This simulates the Verifier (Feedback System) asking for proof.
    """
    user = get_current_user()
    if not user:
        return redirect(url_for('signin'))

    rating = request.form.get('rating')
    comments = request.form.get('comments')
    
    print(f"User {user['id']} submitted feedback. Rating: {rating}, Comments: {comments}")
    
    flash('Thank you for your feedback! Now, let\'s share your proof.', 'info')
    
    credential_id_to_prove = 1 
    
    return redirect(url_for('proof_request', cred_id=credential_id_to_prove))


@app.route('/wallet/proof_request/<int:cred_id>')
def proof_request(cred_id):
    """Renders the screen prompting the user to share a credential (Slide 20, Screen 2)."""
    user = get_current_user()
    if not user:
        return redirect(url_for('signin'))

    conn = get_db_connection()
    credential = conn.execute('SELECT * FROM credentials WHERE id = ? AND holder_id = ?', 
                             (cred_id, user['id'])).fetchone()
    conn.close()
    
    if not credential:
        flash('Requested proof credential not found or access denied.', 'error')
        return redirect(url_for('wallet_dashboard'))
        
    return render_template('proof_request.html', user_id=user['id'], credential=credential)

@app.route('/wallet/share_proof/<int:cred_id>')
def share_proof(cred_id):
    """
    Simulates the Holder confirming the share. 
    This triggers the verification process and redirects to the result screen.
    """
    user = get_current_user()
    if not user:
        return redirect(url_for('signin'))
        
    conn = get_db_connection()
    credential = conn.execute('SELECT * FROM credentials WHERE id = ? AND holder_id = ?', 
                             (cred_id, user['id'])).fetchone()
    conn.close()

    if not credential:
        flash('Credential to share not found.', 'error')
        return redirect(url_for('wallet_dashboard'))

    if credential['is_verified'] == 0:
        
        flash('Proof shared, but verification failed: Credential is still pending official issuance/verification.', 'error')
        return redirect(url_for('verifier_result', cred_id=cred_id, result='failed'))


    flash('Proof successfully shared and verified!', 'success')
    return redirect(url_for('verifier_result', cred_id=cred_id, result='success'))


@app.route('/verifier/result/<int:cred_id>/<string:result>')
def verifier_result(cred_id, result):
    """Renders the Verifier Result screen (Slide 20, Screen 3)."""
    
    user = get_current_user()
    
    user_id_to_fetch = user['id'] if user else 1 
    user_data = get_user_by_id(user_id_to_fetch)

    conn = get_db_connection()
    
    credential = conn.execute('SELECT title FROM credentials WHERE id = ?', (cred_id,)).fetchone()
    conn.close()
    
    if not user_data or not credential:
          flash('Error retrieving verification data.', 'error')
          return redirect(url_for('home'))
        
    return render_template('verifier_result.html', 
                           user=user_data, 
                           credential=credential, 
                           result=result)

@app.route('/wallet/settings')
def user_settings():
    """Renders the user settings page (Profile and Preferences)."""
    
    user = get_current_user()
    user_role = session.get('user_role')
    
    if not user or user_role != 'holder':
        flash('Access denied. Please sign in to view settings.', 'error')
    
        if user_role == 'admin':
            return redirect(url_for('admin_dashboard'))
        elif user_role == 'verifier':
            return redirect(url_for('verifier_dashboard'))
        elif user_role == 'issuer':
            return redirect(url_for('issuer_dashboard'))
        else:
            return redirect(url_for('signin'))
        
    credentials = get_user_credentials(user['id'])
    total_credentials = len(credentials)
    verified_count = sum(1 for cred in credentials if cred['is_verified'] == 1)

    return render_template('user_settings.html',
                           user=user,
                           total_credentials=total_credentials,
                           verified_count=verified_count)

@app.route('/wallet/update_settings', methods=['POST'])
def update_user_settings():
    """Handles updating the user's profile settings (name and location_organisation_id )."""
    user = get_current_user()
    user_role = session.get('user_role')

    if not user or user_role != 'holder':
        flash('Access denied. Please sign in.', 'error')
        return redirect(url_for('signin'))

    full_name = request.form.get('full_name')
    location_organisation_id = request.form.get('location_organisation_id ')
    
    if not full_name:
        flash('Full Name cannot be empty.', 'error')
        return redirect(url_for('user_settings'))

    conn = get_db_connection()
    try:
        conn.execute("""
            UPDATE users SET full_name = ?, location_organisation_id  = ? WHERE id = ?
        """, (full_name, location_organisation_id , user['id']))
        conn.commit()
        flash('Profile settings updated successfully.', 'success')
    except Exception as e:
        flash(f'An error occurred while updating settings: {e}', 'error')
    finally:
        conn.close()

    return redirect(url_for('user_settings'))

@app.route('/admin/issue_credential_action', methods=['POST'])
def admin_issue_credential_action():
    """Handles the form submission from the Admin Dashboard to create a new credential."""
    
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can create credentials.', 'error')
        return redirect(url_for('signin'))

    holder_email = request.form.get('holder_email')
    title = request.form.get('title')
    issue_date = request.form.get('issue_date')
    duration = request.form.get('duration')
    location = request.form.get('location')
    instructor = request.form.get('instructor')
    credential_type = request.form.get('credential_type')

    holder = get_user_by_email(holder_email)

    if not holder:
        flash(f'Error: Holder with email "{holder_email}" not found. Credential not issued.', 'error')
        return redirect(url_for('admin_dashboard'))
    
    conn = get_db_connection()
    try:
        conn.execute("""
            INSERT INTO credentials (holder_id, title, issue_date, duration, location, instructor, credential_type, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            holder['id'],
            title,
            issue_date,
            duration,
            location,
            instructor,
            credential_type,
            0 
        ))
        conn.commit()
        flash(f"Credential '{title}' successfully issued and sent to holder ({holder_email}). It is now pending verification.", 'success')

    except Exception as e:
        flash(f'An error occurred during credential issuance: {e}', 'error')
    finally:
        conn.close()

    return redirect(url_for('admin_dashboard'))

@app.route('/admin/create_user', methods=['POST'])
def admin_create_user():
    """Admin action to create a new user with specified role."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can create users.', 'error')
        return redirect(url_for('signin'))
    
    full_name = request.form.get('full_name')
    company = request.form.get('company')
    location_organisation_id = request.form.get('location_organisation_id')
    email = request.form.get('email')
    password = request.form.get('password')
    role = request.form.get('role')
    
    if not full_name or not email or not password or not role:
        flash('All required fields must be filled.', 'error')
        return redirect(url_for('admin_user_management'))
    
    if role not in ['holder', 'issuer', 'verifier', 'admin']:
        flash('Invalid role selected.', 'error')
        return redirect(url_for('admin_user_management'))
    
    password_hash = generate_password_hash(password)
    
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO users (full_name, location_organisation_id, email, password_hash, role) 
            VALUES (?, ?, ?, ?, ?)
        """, (full_name, location_organisation_id, email, password_hash, role))
        conn.commit()
        
        user_id = cursor.lastrowid
        flash(f"User '{full_name}' ({email}) successfully created with role '{role}'. User ID: {user_id}", 'success')
        
    except sqlite3.IntegrityError:
        flash(f'Email {email} is already registered in the system.', 'error')
    except Exception as e:
        flash(f'An error occurred while creating user: {e}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_user_management'))

@app.route('/admin/create_credential_type', methods=['POST'])
def admin_create_credential_type():
    """Admin action to create a new credential type."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can create credential types.', 'error')
        return redirect(url_for('signin'))
    
    name = request.form.get('name')
    description = request.form.get('description')
    color_code = request.form.get('color_code', '#6B7280')
    icon = request.form.get('icon', '🎓')
    
    if not name:
        flash('Credential type name is required.', 'error')
        return redirect(url_for('admin_credential_types'))
    
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO credential_types (name, description, color_code, icon, created_by) 
            VALUES (?, ?, ?, ?, ?)
        """, (name, description, color_code, icon, session.get('user_id')))
        conn.commit()
        
        type_id = cursor.lastrowid
        flash(f"Credential type '{name}' successfully created. ID: {type_id}", 'success')
        
    except sqlite3.IntegrityError:
        flash(f'Credential type "{name}" already exists in the system.', 'error')
    except Exception as e:
        flash(f'An error occurred while creating credential type: {e}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_credential_types'))

@app.route('/admin/toggle_credential_type', methods=['POST'])
def admin_toggle_credential_type():
    """Admin action to toggle active status of a credential type."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can modify credential types.', 'error')
        return redirect(url_for('signin'))
    
    type_id = request.form.get('type_id')
    
    if not type_id:
        flash('Invalid request: missing credential type ID.', 'error')
        return redirect(url_for('admin_credential_types'))
    
    conn = get_db_connection()
    try:
        # Get current status
        current_type = conn.execute('SELECT name, is_active FROM credential_types WHERE id = ?', (type_id,)).fetchone()
        
        if not current_type:
            flash('Credential type not found.', 'error')
            return redirect(url_for('admin_credential_types'))
        
        # Toggle the status
        new_status = 0 if current_type['is_active'] else 1
        conn.execute('UPDATE credential_types SET is_active = ? WHERE id = ?', (new_status, type_id))
        conn.commit()
        
        status_text = "activated" if new_status else "deactivated"
        flash(f"Credential type '{current_type['name']}' has been {status_text}.", 'success')
        
    except Exception as e:
        flash(f"Error updating credential type: {e}", 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_credential_types'))

@app.route('/admin/delete_credential_type', methods=['POST'])
def admin_delete_credential_type():
    """Admin action to delete a credential type."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can delete credential types.', 'error')
        return redirect(url_for('signin'))
    
    type_id = request.form.get('type_id')
    
    if not type_id:
        flash('Invalid request: missing credential type ID.', 'error')
        return redirect(url_for('admin_credential_types'))
    
    conn = get_db_connection()
    try:
        # Check if credential type exists and get its name
        credential_type = conn.execute('SELECT name FROM credential_types WHERE id = ?', (type_id,)).fetchone()
        
        if not credential_type:
            flash('Credential type not found.', 'error')
            return redirect(url_for('admin_credential_types'))
        
        # Check if any credentials are using this type
        credentials_using_type = conn.execute('SELECT COUNT(*) as count FROM credentials WHERE credential_type = ?', (credential_type['name'],)).fetchone()
        
        if credentials_using_type['count'] > 0:
            flash(f"Cannot delete '{credential_type['name']}' - {credentials_using_type['count']} credentials are using this type.", 'error')
            return redirect(url_for('admin_credential_types'))
        
        # Safe to delete
        conn.execute('DELETE FROM credential_types WHERE id = ?', (type_id,))
        conn.commit()
        
        flash(f"Credential type '{credential_type['name']}' has been deleted.", 'success')
        
    except Exception as e:
        flash(f"Error deleting credential type: {e}", 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_credential_types'))

@app.route('/admin/credential_types')
def admin_credential_types():
    """Admin credential types management dashboard."""
    if session.get('user_role') != 'admin':
        flash('Access denied. You must be logged in as an Admin.', 'error')
        return redirect(url_for('signin'))
    
    conn = get_db_connection()
    
    # Fetch credential types
    credential_types = conn.execute("""
        SELECT ct.*, u.full_name as created_by_name 
        FROM credential_types ct 
        LEFT JOIN users u ON ct.created_by = u.id 
        ORDER BY ct.created_at DESC
    """).fetchall()
    
    conn.close()
    
    return render_template('admin_credential_types.html',
                           user_role=session.get('user_role'),
                           credential_types=credential_types)

@app.route('/admin/visitor_management')
def admin_visitor_management():
    """Admin visitor management dashboard."""
    if session.get('user_role') != 'admin':
        flash('Access denied. You must be logged in as an Admin.', 'error')
        return redirect(url_for('signin'))
    
    conn = get_db_connection()
    
    # Get visitor statistics and recent activity
    recent_visitors = conn.execute("""
        SELECT u.id, u.full_name, u.email, u.location_organisation_id, u.registered_on,
               COUNT(c.id) as credential_count
        FROM users u 
        LEFT JOIN credentials c ON u.id = c.holder_id 
        WHERE u.role = 'holder' AND u.id < 997
        GROUP BY u.id 
        ORDER BY u.registered_on DESC 
        LIMIT 50
    """).fetchall()
    
    # Get visitor stats
    total_visitors = conn.execute('SELECT COUNT(*) as count FROM users WHERE role = "holder" AND id < 997').fetchone()
    recent_visitors_count = conn.execute('SELECT COUNT(*) as count FROM users WHERE role = "holder" AND id < 997 AND registered_on >= date("now", "-7 days")').fetchone()
    
    conn.close()
    
    return render_template('admin_visitor_management.html',
                           user_role=session.get('user_role'),
                           recent_visitors=recent_visitors,
                           total_visitors=total_visitors['count'],
                           recent_visitors_count=recent_visitors_count['count'])

@app.route('/admin/settings')
def admin_settings():
    """Admin settings and configuration dashboard."""
    if session.get('user_role') != 'admin':
        flash('Access denied. You must be logged in as an Admin.', 'error')
        return redirect(url_for('signin'))
    
    conn = get_db_connection()
    
    # Get system statistics
    total_users = conn.execute('SELECT COUNT(*) as count FROM users WHERE id < 997').fetchone()
    total_credentials = conn.execute('SELECT COUNT(*) as count FROM credentials').fetchone()
    pending_verifications = conn.execute('SELECT COUNT(*) as count FROM credentials WHERE is_verified = 0').fetchone()
    
    # Get credential types for management
    credential_types = conn.execute("""
        SELECT ct.*, u.full_name as created_by_name 
        FROM credential_types ct 
        LEFT JOIN users u ON ct.created_by = u.id 
        ORDER BY ct.created_at DESC
    """).fetchall()
    
    conn.close()
    
    return render_template('admin_settings.html',
                           user_role=session.get('user_role'),
                           total_users=total_users['count'],
                           total_credentials=total_credentials['count'],
                           pending_verifications=pending_verifications['count'],
                           credential_types=credential_types)
    
@app.route('/admin/credential_management')
def admin_credential_management():
    """Admin credential templates management dashboard."""
    if session.get('user_role') != 'admin':
        flash('Access denied. You must be logged in as an Admin.', 'error')
        return redirect(url_for('signin'))
    
    conn = get_db_connection()
    
    # Get credential templates/types with usage statistics
    template_types = conn.execute("""
        SELECT tt.*, o.name as organisation_name,
            COUNT(c.id) as usage_count,
            MAX(c.issue_date) as last_used
        FROM template_types tt 
        LEFT JOIN organisations o ON tt.location_organisation_id = o.id
        LEFT JOIN credentials c ON tt.id = c.template_id 
        GROUP BY tt.id 
        ORDER BY tt.template_name
    """).fetchall()
    
    organisations = conn.execute('SELECT * FROM organisations WHERE is_active = 1').fetchall()
    credential_types = conn.execute('SELECT * FROM credential_types').fetchall()
    
    # Get recent credential issuance activity
    recent_issuances = conn.execute("""
        SELECT c.title, c.credential_type, c.issue_date, u.full_name as holder_name
        FROM credentials c 
        JOIN users u ON c.holder_id = u.id 
        ORDER BY c.issue_date DESC 
        LIMIT 10
    """).fetchall()
    
    conn.close()
    
    return render_template('admin_credential_management.html',
                           user_role=session.get('user_role'),
                           organisations=organisations,
                           recent_issuances=recent_issuances,
                           template_types=template_types,
                           credential_types=credential_types)

@app.route('/admin/create_template_type', methods=['POST'])
def admin_create_template_type():
    """Admin action to create a new template type."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can create credential types.', 'error')
        return redirect(url_for('signin'))
    
    template_name = request.form.get('template_name')
    description = request.form.get('description')
    credential_type = request.form.get('credential_type')
    validity_period = request.form.get('validity_period')
    location_organisation_id = request.form.get('location_organisation_id')
    
    if not template_name:
        flash('Template name is required.', 'error')
        return redirect(url_for('admin_dashboard'))
    
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO template_types (template_name, description, credential_type, validity_period, location_organisation_id) 
            VALUES (?, ?, ?, ?, ?)
        """, (template_name, description, credential_type, validity_period, location_organisation_id))
        conn.commit()
        
        type_id = cursor.lastrowid
        flash(f"Template name '{template_name}' successfully created. ID: {type_id}", 'success')
        
    except sqlite3.IntegrityError:
        flash(f'Template name "{template_name}" already exists in the system.', 'error')
    except Exception as e:
        flash(f'An error occurred while creating credential type: {e}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_dashboard'))

@app.route('/admin/toggle_template_type', methods=['POST'])
def admin_toggle_template_type():
    """Admin action to toggle active status of a template type."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can modify credential types.', 'error')
        return redirect(url_for('signin'))
    
    type_id = request.form.get('type_id')
    
    if not type_id:
        flash('Invalid request: missing template type ID.', 'error')
        return redirect(url_for('admin_dashboard'))
    
    conn = get_db_connection()
    try:
        # Get current status
        current_type = conn.execute('SELECT template_name, is_active FROM template_types WHERE id = ?', (type_id,)).fetchone()
        
        if not current_type:
            flash('Template type not found.', 'error')
            return redirect(url_for('admin_dashboard'))
        
        # Toggle the status
        new_status = 0 if current_type['is_active'] else 1
        conn.execute('UPDATE template_types SET is_active = ? WHERE id = ?', (new_status, type_id))
        conn.commit()
        
        status_text = "activated" if new_status else "deactivated"
        flash(f"Template type '{current_type['template_name']}' has been {status_text}.", 'success')
        
    except Exception as e:
        flash(f"Error updating template type: {e}", 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_credential_management.html'))

@app.route('/admin/delete_template_type', methods=['POST'])
def admin_delete_template_type():
    """Admin action to delete a template type."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can delete template types.', 'error')
        return redirect(url_for('signin'))
    
    type_id = request.form.get('type_id')
    
    if not type_id:
        flash('Invalid request: missing template type ID.', 'error')
        return redirect(url_for('admin_dashboard'))
    
    conn = get_db_connection()
    try:
        # Check if template type exists and get its name
        template_type = conn.execute('SELECT template_name FROM template_types WHERE id = ?', (type_id,)).fetchone()
        
        if not template_type:
            flash('Template type not found.', 'error')
            return redirect(url_for('admin_dashboard'))
        
        # Check if any credentials are using this type
        templates_using_type = conn.execute('SELECT COUNT(*) as count FROM template_type WHERE template_type = ?', (template_type['template_name'],)).fetchone()
        
        if templates_using_type['count'] > 0:
            flash(f"Cannot delete '{template_type['template_name']}' - {templates_using_type['count']} credentials are using this type.", 'error')
            return redirect(url_for('admin_dashboard'))
        
        # Safe to delete
        conn.execute('DELETE FROM template_types WHERE id = ?', (type_id,))
        conn.commit()
        
        flash(f"Template type '{template_type['template_name']}' has been deleted.", 'success')
        
    except Exception as e:
        flash(f"Error deleting template type: {e}", 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_credential_management.html'))

# Current: /register
# Required: /organisation/:organisation_ID/visitor_register
@app.route('/organisation/<int:organisation_id>/visitor_register')
def organisation_visitor_register(organisation_id):
    # Auto-populate organisation field from URL
    pass

# Organisation Management View functions

@app.route('/admin/create_organisation', methods=['POST'])
def admin_create_organisation():
    """Admin action to create a new organisation."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can create organisations.', 'error')
        return redirect(url_for('signin'))
    
    name = request.form.get('name')
    company_name = request.form.get('company_name')
    description = request.form.get('description')
    
    if not name or not company_name:
        flash('Organisation name and company name are required.', 'error')
        return redirect(url_for('admin_organisation_management'))
    
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO organisations (name, company_name, description) 
            VALUES (?, ?, ?)
        """, (name, company_name, description))
        conn.commit()
        
        org_id = cursor.lastrowid
        flash(f"Organisation '{name}' successfully created. ID: {org_id}", 'success')
        
    except sqlite3.IntegrityError:
        flash(f'Organisation "{name}" already exists in the system.', 'error')
    except Exception as e:
        flash(f'An error occurred while creating organisation: {e}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_organisation_management'))

@app.route('/admin/update_organisation/<int:org_id>', methods=['POST'])
def admin_update_organisation(org_id):
    """Admin action to update an existing organisation."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can update organisations.', 'error')
        return redirect(url_for('signin'))

    # Get form data
    name = request.form.get('name')
    company_name = request.form.get('company_name')
    description = request.form.get('description')

    # Validate input
    if not name or not company_name:
        flash('Organisation name and company name are required.', 'error')
        return redirect(url_for('admin_organisation_management'))

    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            UPDATE organisations
            SET name = ?, company_name = ?, description = ?
            WHERE id = ?
        """, (name, company_name, description, org_id))
        conn.commit()

        if cursor.rowcount == 0:
            flash('Organisation not found or no changes made.', 'warning')
        else:
            flash(f"Organisation '{name}' successfully updated.", 'success')

    except sqlite3.IntegrityError:
        flash(f'Organisation "{name}" already exists in the system.', 'error')
    except Exception as e:
        flash(f'An error occurred while updating organisation: {e}', 'error')
    finally:
        conn.close()

    return redirect(url_for('admin_organisation_management'))

@app.route('/admin/toggle_organisation', methods=['POST'])
def admin_toggle_organisation():
    """Admin action to toggle active status of an organisation."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can modify organisations.', 'error')
        return redirect(url_for('signin'))
    
    org_id = request.form.get('org_id')
    
    if not org_id:
        flash('Invalid request: missing organisation ID.', 'error')
        return redirect(url_for('admin_organisation_management'))
    
    conn = get_db_connection()
    try:
        # Get current status
        current_org = conn.execute('SELECT name, is_active FROM organisations WHERE id = ?', (org_id,)).fetchone()
        
        if not current_org:
            flash('Organisation not found.', 'error')
            return redirect(url_for('admin_organisation_management'))
        
        # Toggle the status
        new_status = 0 if current_org['is_active'] else 1
        conn.execute('UPDATE organisations SET is_active = ? WHERE id = ?', (new_status, org_id))
        conn.commit()
        
        status_text = "activated" if new_status else "deactivated"
        flash(f"Organisation '{current_org['name']}' has been {status_text}.", 'success')
        
    except Exception as e:
        flash(f"Error updating organisation: {e}", 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_organisation_management'))

@app.route('/admin/delete_organisation', methods=['POST'])
def admin_delete_organisation():
    """Admin action to delete an organisation."""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can delete organisations.', 'error')
        return redirect(url_for('signin'))
    
    org_id = request.form.get('org_id')
    
    if not org_id:
        flash('Invalid request: missing organisation ID.', 'error')
        return redirect(url_for('admin_organisation_management'))
    
    conn = get_db_connection()
    try:
        # Check if organisation exists
        organisation = conn.execute('SELECT name FROM organisations WHERE id = ?', (org_id,)).fetchone()
        
        if not organisation:
            flash('Organisation not found.', 'error')
            return redirect(url_for('admin_organisation_management'))
        
        # Check if any users are linked to this organisation
        users_count = conn.execute('SELECT COUNT(*) as count FROM users WHERE location_organisation_id = ?', (org_id,)).fetchone()
        templates_count = conn.execute('SELECT COUNT(*) as count FROM template_types WHERE location_organisation_id = ?', (org_id,)).fetchone()
        
        if users_count['count'] > 0 or templates_count['count'] > 0:
            flash(f"Cannot delete '{organisation['name']}' - {users_count['count']} users and {templates_count['count']} templates are linked to this organisation.", 'error')
            return redirect(url_for('admin_organisation_management'))
        
        # Safe to delete
        conn.execute('DELETE FROM organisations WHERE id = ?', (org_id,))
        conn.commit()
        
        flash(f"Organisation '{organisation['name']}' has been deleted.", 'success')
        
    except Exception as e:
        flash(f"Error deleting organisation: {e}", 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_organisation_management'))

@app.route('/admin/organisation_management')
def admin_organisation_management():
    """Admin organisation management dashboard."""
    if session.get('user_role') != 'admin':
        flash('Access denied. You must be logged in as an Admin.', 'error')
        return redirect(url_for('signin'))
    
    conn = get_db_connection()
    
    # Get all organisations with statistics
    organisations = conn.execute("""
        SELECT o.*, 
               COUNT(DISTINCT u.id) as user_count,
               COUNT(DISTINCT tt.id) as template_count,
               COUNT(DISTINCT c.id) as credential_count
        FROM organisations o
        LEFT JOIN users u ON o.id = u.location_organisation_id
        LEFT JOIN template_types tt ON o.id = tt.location_organisation_id
        LEFT JOIN credentials c ON u.id = c.holder_id
        GROUP BY o.id
        ORDER BY o.created_at DESC
    """).fetchall()
    
    conn.close()
    
    return render_template('admin_organisation_management.html',
                           user_role=session.get('user_role'),
                           organisations=organisations)

# Admin Templates View Functions

@app.route('/admin/get_template/<int:template_id>')
def get_template_data(template_id):
    """API endpoint to get template data for modal population"""
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Access denied'}), 403
    
    conn = get_db_connection()
    template = conn.execute("""
        SELECT tt.*, o.name as organisation_name
        FROM template_types tt 
        LEFT JOIN organisations o ON tt.location_organisation_id = o.id
        WHERE tt.id = ?
    """, (template_id,)).fetchone()
    conn.close()
    
    if template:
        return jsonify({
            'id': template['id'],
            'template_name': template['template_name'],
            'description': template['description'],
            'credential_type': template['credential_type'],
            'validity_period': template['validity_period'],
            'location_organisation_id': template['location_organisation_id'],
            'organisation_name': template['organisation_name'] or 'No Organisation'
        })
    return jsonify({'error': 'Template not found'}), 404

@app.route('/admin/issue_from_template', methods=['POST'])
def admin_issue_from_template():
    """Issue credential from template with pre-filled data"""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can issue credentials.', 'error')
        return redirect(url_for('signin'))
    
    template_id = request.form.get('template_id')
    holder_email = request.form.get('holder_email')
    issue_date = request.form.get('issue_date')
    instructor = request.form.get('instructor')
    
    if not all([template_id, holder_email, issue_date, instructor]):
        flash('All fields are required.', 'error')
        return redirect(url_for('admin_credential_management'))
    
    conn = get_db_connection()
    
    # Get template data
    template = conn.execute("""
        SELECT tt.*, o.name as organisation_name
        FROM template_types tt 
        LEFT JOIN organisations o ON tt.location_organisation_id = o.id
        WHERE tt.id = ?
    """, (template_id,)).fetchone()
    
    template.usage_count = (template.usage_count or 0) + 1
    
    if not template:
        flash('Template not found.', 'error')
        return redirect(url_for('admin_credential_management'))
    
    # Get holder
    holder = conn.execute('SELECT * FROM users WHERE email = ?', (holder_email,)).fetchone()
    if not holder:
        flash(f'No user found with email: {holder_email}', 'error')
        return redirect(url_for('admin_credential_management'))
    
    try:
        conn.execute("""
            INSERT INTO credentials (holder_id, title, issue_date, duration, location, instructor, credential_type, template_id, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            holder['id'],
            template['template_name'],
            issue_date,
            template['validity_period'],
            template['organisation_name'] or 'No Location',
            instructor,
            template['credential_type'],
            template_id,
            0 
        ))
        conn.commit()
        flash(f"Credential '{template['template_name']}' successfully issued to {holder_email}.", 'success')

    except Exception as e:
        flash(f'An error occurred during credential issuance: {e}', 'error')
    finally:
        conn.close()

    return redirect(url_for('admin_credential_management'))

@app.route('/admin/edit_template/<int:template_id>', methods=['POST'])
def admin_edit_template(template_id):
    """Update existing template"""
    if session.get('user_role') != 'admin':
        flash('Access denied. Only Admins can edit templates.', 'error')
        return redirect(url_for('signin'))
    
    template_name = request.form.get('template_name')
    description = request.form.get('description')
    credential_type = request.form.get('credential_type')
    validity_period = request.form.get('validity_period')
    location_organisation_id = request.form.get('location_organisation_id')
    
    if not template_name:
        flash('Template name is required.', 'error')
        return redirect(url_for('admin_credential_management'))
    
    conn = get_db_connection()
    try:
        # Check if template exists
        existing_template = conn.execute('SELECT template_name FROM template_types WHERE id = ?', (template_id,)).fetchone()
        
        if not existing_template:
            flash('Template not found.', 'error')
            return redirect(url_for('admin_credential_management'))
        
        # Update template
        conn.execute("""
            UPDATE template_types 
            SET template_name = ?, description = ?, credential_type = ?, validity_period = ?, location_organisation_id = ?
            WHERE id = ?
        """, (template_name, description, credential_type, validity_period, location_organisation_id, template_id))
        conn.commit()
        
        flash(f"Template '{template_name}' successfully updated.", 'success')
        
    except sqlite3.IntegrityError:
        flash(f'Template name "{template_name}" already exists in the system.', 'error')
    except Exception as e:
        flash(f'An error occurred while updating template: {e}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('admin_credential_management'))

if __name__ == '__main__':
    app.run(debug=True)
