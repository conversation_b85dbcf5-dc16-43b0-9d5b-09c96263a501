<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Template Type Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Inter', sans-serif; 
            background-color: #f3f4f6; 
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background-color: #fff; 
            min-height: 100vh; 
            padding: 20px; 
            box-shadow: 0 0 10px rgba(0,0,0,0.05); 
        }
        /* Center Modal with Flexbox */
        .modal { 
            display: none; 
            position: fixed; 
            z-index: 1000; 
            left: 0; 
            top: 0; 
            width: 100%; 
            height: 100%; 
            background-color: rgba(0,0,0,0.4); 
            align-items: center; 
            justify-content: center; 
        }
        .modal-content { 
            background-color: #fff; 
            padding: 20px; 
            border-radius: 10px; 
            width: 90%; 
            max-width: 600px; 
            position: relative; 
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }
        .close-modal-btn { 
            position: absolute; 
            right: 15px; 
            top: 10px; 
            font-size: 24px; 
            color: #999; 
            cursor: pointer; 
        }
        .btn-primary { 
            background-color: #00bfa5; 
            color: white; 
            padding: 10px 16px; 
            border-radius: 8px; 
            font-weight: 600; 
            transition: 0.3s; 
        }
        .btn-primary:hover { 
            background-color: #009e8e; 
        }
        .btn-secondary { 
            background-color: #6b7280; 
            color: white; 
            padding: 10px 16px; 
            border-radius: 8px; 
            font-weight: 600; 
            transition: 0.3s; 
            border: none;
            cursor: pointer;
        }
        .btn-secondary:hover { 
            background-color: #4b5563; 
        }
        /* Navigation bar styling */ 
        .nav-tab {
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.3s;
        border: 1px solid #e5e7eb;
        }

        .nav-tab:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Template Type Management</h1>
            <a href="{{ url_for('admin_dashboard') }}" class="text-sm text-blue-600 hover:underline">← Back to Dashboard</a>
        </div>

        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-tab">Dashboard</a>
                <a href="{{ url_for('admin_organisation_management') }}" class="nav-tab">Organisation Management</a>
                <a href="{{ url_for('admin_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('admin_visitor_management') }}" class="nav-tab">Visitor Management</a>
                <a href="{{ url_for('admin_credential_types') }}" class="nav-tab">Credentials Types</a>
                <a href="{{ url_for('admin_credential_management') }}" class="nav-tab active">Credential Template Management</a>
            </div>
        </nav>

        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="mb-4">
                {% for category, message in messages %}
                    <div class="p-3 mb-2 rounded-lg text-sm 
                        {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        {% endwith %}

                <!-- Modal: Create Template -->
                <div id="create-template-modal" class="modal">
                    <div class="modal-content">
                        <span class="close-modal-btn" data-modal-target="create-template-modal">&times;</span>
                        <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Create New Credential Template</h2>
                        
                        <form method="POST" action="{{ url_for('admin_create_template_type') }}">
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Template Name</label>
                                <input type="text" name="template_name" required class="w-full border rounded px-3 py-2">
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Description</label>
                                <input type="text" name="description" class="w-full border rounded px-3 py-2">
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Credential Type</label>
                                <select name="credential_type" required class="w-full border rounded px-3 py-2">
                                    <option value="">Select Credential Type</option>
                                    {% for c in credential_types %}
                                        <option value="{{ c.name }}">{{ c.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Validity Period</label>
                                <input type="text" name="validity_period" required placeholder="e.g. Permanent" class="w-full border rounded px-3 py-2">
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Location/Organisation</label>
                                <select name="location_organisation_id" required class="w-full border rounded px-3 py-2">
                                    <option value="">Select Organisation</option>
                                    {% for org in organisations %}
                                        <option value="{{ org.id }}">{{ org.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <button type="submit" class="btn-primary w-full">Save Template</button>
                        </form>
                    </div>
                </div>

        <div class="mb-6">
            <button class="btn-primary open-modal-btn" data-modal-target="create-template-modal">➕ Create New Template Type</button>
        </div>

        <div class="bg-gray-50 border rounded-lg shadow-sm p-4">
            <h2 class="text-lg font-semibold mb-3 text-gray-700">
                Existing Template Types ({{ template_types|length if template_types else 0 }})
            </h2>

        {% if template_types %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for template in template_types %}
                <div class="bg-white rounded-lg shadow-md border p-6">
                    <!-- Template Header -->
                    <div class="flex justify-between items-start mb-4">
                        <h3 class="font-semibold text-lg text-gray-800">{{ template.template_name }}</h3>
                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ template.credential_type }}</span>
                    </div>
                    
                    <!-- Template Details -->
                    <div class="space-y-2 mb-4">
                        <p class="text-sm text-gray-600">{{ template.description or "No description" }}</p>
                        <p class="text-xs text-gray-500">Duration: {{ template.validity_period }}</p>
                        <p class="text-xs text-gray-500">Location: {{ template.organisation_name }}</p>
                    </div>
                    
                    <!-- Usage Metrics -->
                    <div class="bg-gray-50 rounded p-3 mb-4">
                        <p class="text-sm font-medium text-gray-700">Usage: {{ template.usage_count or 0 }} times</p>
                        {% if template.last_used %}
                            <p class="text-xs text-gray-500">Last used: {{ template.last_used }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex space-x-2">
                        <button 
                            class="btn-secondary flex-1 open-modal-btn" 
                            data-modal-target="edit-template-modal"
                            data-template-id="{{ template.id }}"
                            data-template-name="{{ template.template_name }}"
                            data-description="{{ template.description }}"
                            data-credential-type="{{ template.credential_type }}"
                            data-validity-period="{{ template.validity_period }}"
                            data-organisation-id="{{ template.organisation_id }}"
                        >
                            ✏️ Edit
                        </button>
                        <button   class="btn-primary flex-1 open-modal-btn" 
                                data-modal-target="issue-from-template-modal"
                                data-template-id="{{ template.id }}"
                                data-template-name="{{ template.template_name }}"
                                data-credential-type="{{ template.credential_type }}"
                                data-validity-period="{{ template.validity_period }}"
                                data-organisation="{{ template.organisation_name }}"
                                >
                            ➕ Issue
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Modal: Issue From Template -->
            <div id="issue-from-template-modal" class="modal">
                <div class="modal-content">
                    <span class="close-modal-btn" data-modal-target="issue-from-template-modal">&times;</span>
                    <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Issue New Credential</h2>
                    
                    <form method="POST" action="{{ url_for('admin_issue_from_template') }}">
                        <input type="hidden" id="template_id" name="template_id" value="">
                        
                        <!-- Pre-filled Read-Only Fields -->
                        <div class="form-group mb-4">
                            <label class="block text-sm font-semibold mb-1">Template Title</label>
                            <input type="text" id="template_title" name="template_title" readonly class="w-full border rounded px-3 py-2 bg-gray-100">
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-semibold mb-1">Credential Type</label>
                            <input type="text" id="credential_type_display" name="credential_type_display" readonly class="w-full border rounded px-3 py-2 bg-gray-100">
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-semibold mb-1">Duration</label>
                            <input type="text" id="duration_display" name="duration_display" readonly class="w-full border rounded px-3 py-2 bg-gray-100">
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-semibold mb-1">Location/Organisation</label>
                            <input type="text" id="location_organisation_display" name="location_organisation_display" readonly class="w-full border rounded px-3 py-2 bg-gray-100">
                        </div>
                        
                        <!-- Editable Fields -->
                        <div class="form-group mb-4">
                            <label class="block text-sm font-semibold mb-1">Holder Email Address</label>
                            <input type="email" name="holder_email" required class="w-full border rounded px-3 py-2" placeholder="<EMAIL>">
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-semibold mb-1">Issue Date</label>
                            <input type="date" name="issue_date" required class="w-full border rounded px-3 py-2" value="{{ now.strftime('%Y-%m-%d') if now else '' }}">
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-semibold mb-1">Instructor</label>
                            <input type="text" name="instructor" required class="w-full border rounded px-3 py-2" placeholder="Dr. John Smith">
                        </div>
                        
                        <button type="submit" class="btn-primary w-full">Issue New Credential</button>
                    </form>
                </div>

                <!-- Modal: Edit Template -->
                <div id="edit-template-modal" class="modal">
                    <div class="modal-content">
                        <span class="close-modal-btn" data-modal-target="edit-template-modal">&times;</span>
                        <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">Edit Credential Template</h2>
                        
                        <form method="POST" action="" id="edit-template-form">
                            <input type="hidden" id="edit_template_id" name="template_id" value="">
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Template Name</label>
                                <input type="text" id="edit_template_name" name="template_name" required class="w-full border rounded px-3 py-2">
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Description</label>
                                <input type="text" id="edit_description" name="description" class="w-full border rounded px-3 py-2">
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Credential Type</label>
                                <select id="edit_credential_type" name="credential_type" required class="w-full border rounded px-3 py-2">
                                    <option value="">Select Credential Type</option>
                                    {% for c in credential_types %}
                                        <option value="{{ c.name }}">{{ c.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Validity Period</label>
                                <input type="text" id="edit_validity_period" name="validity_period" required class="w-full border rounded px-3 py-2">
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="block text-sm font-semibold mb-1">Location/Organisation</label>
                                <select id="edit_location_organisation_id" name="location_organisation_id" required class="w-full border rounded px-3 py-2">
                                    <option value="">Select Organisation</option>
                                    {% for org in organisations %}
                                        <option value="{{ org.id }}">{{ org.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <button type="submit" class="btn-primary w-full">Update Template</button>
                        </form>
                    </div>
                </div>

            </div>
                </div>
            {% else %}
                <p class="text-gray-500 text-center py-4">No template types available yet.</p>
            {% endif %}
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/modal.js') }}"></script>
    <script>
// Function to populate issue modal with template data
function populateIssueModal(templateId) {
    fetch(`/admin/get_template/${templateId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error loading template data: ' + data.error);
                return;
            }
            
            // Populate hidden template ID
            document.getElementById('template_id').value = data.id;
            
            // Populate read-only fields with template data
            document.getElementById('template_title').value = data.template_name;
            document.getElementById('credential_type_display').value = data.credential_type;
            document.getElementById('duration_display').value = data.validity_period;
            document.getElementById('location_organisation_display').value = data.organisation_name;
            
            // Clear editable fields
            document.querySelector('input[name="holder_email"]').value = '';
            document.querySelector('input[name="instructor"]').value = '';
            // Issue date keeps its default value
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading template data');
        });
}

// Function to populate edit modal with template data
function populateEditModal(templateId) {
    fetch(`/admin/get_template/${templateId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error loading template data: ' + data.error);
                return;
            }
            
            // Populate form fields
            document.getElementById('edit_template_id').value = data.id;
            document.getElementById('edit_template_name').value = data.template_name;
            document.getElementById('edit_description').value = data.description || '';
            document.getElementById('edit_validity_period').value = data.validity_period;
            
            // Set dropdown selections
            document.getElementById('edit_credential_type').value = data.credential_type;
            document.getElementById('edit_location_organisation_id').value = data.location_organisation_id;
            
            // Update form action URL
            document.getElementById('edit-template-form').action = `/admin/edit_template/${data.id}`;
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading template data');
        });
}

// Enhanced modal event handling
document.addEventListener('DOMContentLoaded', function() {
    // Listen for modal button clicks
    document.body.addEventListener('click', function(event)) {
        let target = event.target;
        
        // Check if the clicked element is a modal button with template ID
        if (target.classList.contains('open-modal-btn') && target.dataset.templateId) {
            const templateId = target.dataset.templateId;
            const modalId = target.dataset.modalTarget;
            
            // Open modal first
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'flex';
                
                // Populate modal based on type
                if (modalId === 'issue-from-template-modal') {
                    populateIssueModal(templateId);
                } else if (modalId === 'edit-template-modal') {
                    document.getElementById('edit_template_id').value = target.dataset.templateId;
                    document.getElementById('edit_template_name').value = target.dataset.templateName;
                    document.getElementById('edit_description').value = target.dataset.description;
                    document.getElementById('edit_credential_type').value = target.dataset.credentialType;
                    document.getElementById('edit_validity_period').value = target.dataset.validityPeriod;
                    document.getElementById('edit_location_organisation_id').value = target.dataset.organisationId;
                    document.getElementById('edit-template-form').action = `/admin_update_template/${target.dataset.templateId}`;
                    }
                }
            }
        }
    });
    
    
    document.addEventListener("DOMContentLoaded", function() {
        document.body.addEventListener("click", function (event) {
            let target = event.target;

            if (target.classList.contains('open-modal-btn')) {
            const modalId = target.dataset.modalTarget;
            const modal = document.getElementById(modalId);
            if (modal) modal.style.display = 'flex';

            // Prefill fields if data attributes exist
            if (modalId === 'issue-from-template-modal') {
                document.getElementById('template_id').value = target.dataset.templateId;
                document.getElementById('template_title').value = target.dataset.templateName;
                document.getElementById('credential_type_display').value = target.dataset.credentialType;
                document.getElementById('duration_display').value = target.dataset.validityPeriod;
                document.getElementById('location_organisation_display').value = target.dataset.organisation;
            }
            }

            if (target.classList.contains('close-modal-btn') || target.classList.contains('modal')) {
            const modal = target.closest('.modal');
            if (modal) modal.style.display = 'none';
            }
    });

</script>
</body>
</html>
