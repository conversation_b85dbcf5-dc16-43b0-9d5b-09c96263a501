<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Role Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 800px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: 700; color: #1f2937; }
        .user-card { background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 12px; padding: 16px; margin-bottom: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }
        .user-info { margin-bottom: 8px; }
        .user-name { font-weight: 600; font-size: 16px; color: #1f2937; }
        .user-role { padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 700; text-transform: uppercase; }
        
        .role-holder { background-color: #bfdbfe; color: #1e40af; }
        .role-issuer { background-color: #d1fae5; color: #065f46; }
        .role-verifier { background-color: #fef3c7; color: #92400e; }
        .role-admin { background-color: #fecaca; color: #991b1b; }

        .update-form { display: flex; gap: 10px; align-items: center; }
        .update-form select { border: 1px solid #d1d5db; border-radius: 6px; padding: 8px; font-size: 14px; }
        .btn-update { background-color: #4f46e5; color: white; padding: 8px 12px; border-radius: 6px; font-weight: 600; font-size: 14px; transition: background-color 0.3s; }
        .btn-update:hover { background-color: #4338ca; }

        .form-group label { display: block; margin-bottom: 6px; font-size: 14px; font-weight: 600; color: #374151; }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            margin-bottom: 15px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .btn-primary {
            width: 100%;
            padding: 12px;
            background-color: #00bfa5; /* Teal/Cyan-like color for issuance */
            color: white;
            font-weight: 700;
            border-radius: 10px;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #00897b;
        }

        /* Basic modal styling */
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
        }

        /* Modal Content */
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto; /* 15% from the top and centered */
            padding: 20px;
            border: 1px solid #888;
            width: 80%; /* Could be more or less, depending on screen size */
        }

        /* The Close Button */
        .close-modal-btn {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 10px;
            right: 15px;
        }

        .close-modal-btn:hover,
        .close-modal-btn:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        
        /* Navigation bar styling */ 
        .nav-tab {
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.3s;
        border: 1px solid #e5e7eb;
        }

        .nav-tab:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 640px) {
            .container { padding: 10px; }
            .title { font-size: 20px; }
            .user-card { display: block; }
            .update-form { flex-direction: column; align-items: stretch; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">Hybrid Credential System - Administration</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Add this navigation section after the header -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-tab active">Dashboard</a>
                <a href="{{ url_for('admin_organisation_management') }}" class="nav-tab">Organisation Management</a>
                <a href="{{ url_for('admin_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('admin_visitor_management') }}" class="nav-tab">Visitor Management</a>
                <a href="{{ url_for('admin_credential_types') }}" class="nav-tab">Credentials Types</a>
                <a href="{{ url_for('admin_credential_management') }}" class="nav-tab">Credential Template Management</a>
            </div>
        </nav>

        <!-- Dashboard Summary Tiles -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">

            <!-- Total Visits -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Total Visits</h3>
                <p class="text-3xl font-bold text-gray-900">Metrics Placeholder</p>
            </div>

            <!-- Credentials Issued -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Credentials Issued</h3>
                <p class="text-3xl font-bold text-gray-900">Metrics Placeholder</p>
            </div>

            <!-- Quick Actions -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Quick Actions</h3>
                <div class="flex flex-col gap-3">
                    <a href="{{ url_for('admin_visitor_management') }}" 
                    class="w-full py-2 px-4 bg-teal-500 text-white font-semibold rounded-lg text-center hover:bg-teal-600 transition">
                        Register Visitor
                    </a>
                    <a href="{{ url_for('admin_credential_management') }}" 
                    class="w-full py-2 px-4 bg-blue-500 text-white font-semibold rounded-lg text-center hover:bg-blue-600 transition">
                        Create Template
                    </a>
                </div>
            </div>
        </div>

        <!-- Analytics Placeholders -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">

            <!-- Monthly Visits Placeholder -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Monthly Visits</h3>
                <div class="h-48 flex items-center justify-center text-gray-400 italic">
                    Chart Placeholder
                </div>
            </div>

            <!-- Visitor Type Distribution -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Visitor Type Distribution</h3>
                <div class="h-48 flex items-center justify-center text-gray-400 italic">
                    Bar Chart Placeholder
                </div>
            </div>

            <!-- Top Activities -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200 md:col-span-2">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Top Activities</h3>
                <div class="h-48 flex items-center justify-center text-gray-400 italic">
                    Activity List or Chart Placeholder
                </div>
            </div>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-4">
                    {% for category, message in messages %}
                        <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

            <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}
        </p>
    </div>
    
    <script src="{{ url_for('static', filename='js/modal.js') }}"></script>

</body>
</html>
